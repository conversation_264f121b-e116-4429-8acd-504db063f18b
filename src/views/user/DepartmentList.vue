<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import {
  createDepartment,
  getDepartments,
  updateDepartment,
} from "../../services/department";
import {
  createUser,
  getDepartmentUsers,
  updateUser,
  UpdateUserRequest,
} from "../../services/user";
import { getRoleSimple } from "../../services/role";
import type { Department } from "../../types/department";
import type { User } from "../../types/user";
import type { RoleSimple } from "../../services/role";
import { userStatusMap, userStatusSeverityMap } from "../../utils/const";
import { Fluid } from "primevue";

const departments = ref<Department[]>([]);
const selectedDepartment = ref<{ key: string; label: string } | null>(null);
const selectedValue = ref<{ key: string; label: string } | null>(null);
const users = ref<User[]>([]);
const loading = ref(false);
const userLoading = ref(false);
const toast = useToast();
const treeNodes = ref<{ key: string; label: string; children?: any[] }[]>([]);
const selectedKey = ref<{ [key: string]: boolean }>({});
const roles = ref<RoleSimple[]>([]);

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});
const totalRecords = ref(0);

// 加载部门数据
const loadDepartments = async () => {
  try {
    loading.value = true;
    const response = await getDepartments();
    departments.value = response.data;
    treeNodes.value = convertToTreeNodes(response.data);
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载部门数据失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 加载用户数据
const loadUsers = async (departmentId: number) => {
  try {
    userLoading.value = true;
    const params = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };
    const response = await getDepartmentUsers(departmentId, params);
    users.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载用户数据失败",
      life: 3000,
    });
  } finally {
    userLoading.value = false;
  }
};

// 处理部门选择
const onNodeSelect = (node: { key: string; label: string }) => {
  selectedDepartment.value = node;
  selectedKey.value = { [node.key]: true };
  lazyParams.value.page = 1;
  loadUsers(parseInt(node.key));
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  if (selectedDepartment.value) {
    loadUsers(parseInt(selectedDepartment.value.key));
  }
};

// 转换函数
const convertToTreeNodes = (
  data: Department[]
): { key: string; label: string; children?: any[] }[] => {
  if (!data || data.length === 0) {
    return [];
  }

  return data.map((node) => {
    const treeNode = {
      key: node.id.toString(),
      label: node.department_name,
    };

    if (node.children && node.children.length > 0) {
      Object.assign(treeNode, { children: convertToTreeNodes(node.children) });
    }

    return treeNode;
  });
};

// 加载角色数据
const loadRoles = async () => {
  try {
    const response = await getRoleSimple();
    roles.value = response.data;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载角色数据失败",
      life: 3000,
    });
  }
};

onMounted(() => {
  loadDepartments();
  loadRoles();
});

// 新增用户对话框控制
const userDrawer = ref(false);
const editMode = ref(false);
const userForm = ref<{
  id?: number;
  username: string;
  password: string;
  email: string;
  mobile: string;
  role_ids: number[];
}>({
  username: "",
  password: "",
  email: "",
  mobile: "",
  role_ids: [],
});
const submitted = ref(false);

// 打开新增用户对话框
const openNew = () => {
  userForm.value = {
    username: "",
    password: "",
    email: "",
    mobile: "",
    role_ids: [],
  };
  editMode.value = false;
  submitted.value = false;
  userDrawer.value = true;
};

// 打开编辑用户对话框
const openEdit = (user: User) => {
  userForm.value = {
    id: user.id,
    username: user.username,
    password: "", // 编辑时不显示密码
    email: user.email,
    mobile: user.mobile,
    role_ids: user.role_ids, // 暂时为空，需要从后端获取用户的角色列表
  };
  editMode.value = true;
  submitted.value = false;
  userDrawer.value = true;
};

// 隐藏对话框
const hideDialog = () => {
  userDrawer.value = false;
  submitted.value = false;
  editMode.value = false;
};

// 新增/修改部门相关状态
const departmentForm = ref<{
  id?: number;
  department_name: string;
  parent_id?: number | null;
}>({
  department_name: "",
  parent_id: null,
});
const departmentDrawerVisible = ref(false);
const fieldErrors = ref<{ [key: string]: string }>({});

// 打开添加部门抽屉
const openAddDepartment = (node?: any) => {
  departmentForm.value = {
    department_name: "",
    parent_id: node ? parseInt(node.key) : null,
  };
  fieldErrors.value = {};
  departmentDrawerVisible.value = true;
};

// 打开编辑部门抽屉
const openEditDepartment = (node?: any) => {
  departmentForm.value = {
    id: parseInt(node.key),
    department_name: node.label,
  };
  fieldErrors.value = {};
  departmentDrawerVisible.value = true;
};

// 提交新建部门
const saveDepartment = async () => {
  try {
    let response;
    if (departmentForm.value.id) {
      // 编辑角色时调用updateRole方法
      response = await updateDepartment(
        departmentForm.value,
        departmentForm.value.id
      );
    } else {
      // 新增角色时调用createRole方法
      response = await createDepartment(departmentForm.value);
    }
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: departmentForm.value.id ? "部门更新成功" : "部门创建成功",
        life: 3000,
      });
    }
    departmentDrawerVisible.value = false;
    loadDepartments(); // 刷新部门树
  } catch (error: any) {
    if (error.response.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: departmentForm.value.id ? "部门更新失败" : "部门创建失败",
        life: 3000,
      });
    }
  }
};

// 保存用户
const saveUser = async () => {
  submitted.value = true;
  try {
    const departmentId = selectedValue.value
      ? Object.keys(selectedValue.value)[0]
      : selectedDepartment.value
      ? selectedDepartment.value.key
      : "";
    if (!departmentId) {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "请选择部门",
        life: 3000,
      });
      return;
    }
    let response;
    if (userForm.value.id) {
      // 编辑角色时调用updateRole方法
      const updateData: UpdateUserRequest = {
        username: userForm.value.username,
        email: userForm.value.email,
        mobile: userForm.value.mobile,
        role_ids: userForm.value.role_ids,
      };
      response = await updateUser(
        userForm.value.id,
        parseInt(departmentId),
        updateData
      );
    } else {
      // 新增角色时调用createRole方法
      response = await createUser(userForm.value, parseInt(departmentId));
    }
    if (response.code === 200) {
      toast.add({
        severity: "success",
        summary: "成功",
        detail: userForm.value.id ? "用户更新成功" : "用户创建成功",
        life: 3000,
      });
    }
    userDrawer.value = false;
    // 重新加载用户列表
    if (selectedDepartment.value) {
      loadUsers(parseInt(selectedDepartment.value.key));
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: editMode.value ? "更新用户失败" : "创建用户失败",
      life: 3000,
    });
  }
};
</script>

<template>
  <div class="department-list-container">
    <Toast />
    <div class="card">
      <div class="card-header">
        <Message variant="simple" size="large">部门管理</Message>
      </div>
      <div class="department-content">
        <!-- 左侧部门树 -->
        <div class="department-tree">
          <div class="user-list-header">
            <Message variant="simple">部门</Message>
            <Button
              label="新增部门"
              icon="pi pi-plus"
              class="p-button-success"
              @click="openAddDepartment"
            />
          </div>
          <div class="tree-container">
            <Tree
              v-model:selectionKeys="selectedKey"
              :value="treeNodes"
              :loading="loading"
              @nodeSelect="onNodeSelect"
              selectionMode="single"
              class="w-full"
              :highlight-on-select="true"
            >
              <template #default="slotProps">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: right;
                  "
                >
                  <span>{{ slotProps.node.label }}</span>
                  <div>
                    <Button
                      icon="pi pi-plus"
                      class="p-button-text p-button-sm"
                      @click.stop="openAddDepartment(slotProps.node)"
                    />
                    <Button
                      icon="pi pi-pencil"
                      class="p-button-text p-button-sm"
                      @click.stop="openEditDepartment(slotProps.node)"
                    />
                  </div>
                </div>
              </template>
            </Tree>
          </div>
        </div>
        <!-- 右侧用户列表 -->
        <div class="user-list">
          <div class="user-list-header">
            <Message variant="simple">用户列表</Message>
            <Button
              label="新增用户"
              icon="pi pi-plus"
              class="p-button-success"
              @click="openNew"
            />
          </div>
          <DataTable
            class="user-list-table"
            :value="users"
            :lazy="true"
            :paginator="true"
            :rows="20"
            :rowsPerPageOptions="[10, 20, 50]"
            :totalRecords="totalRecords"
            :loading="userLoading"
            @page="onPage($event)"
            stripedRows
            v-if="selectedDepartment"
            scrollable
          >
            <template #empty>
              <div class="empty-message">
                <i
                  class="pi pi-inbox"
                  style="
                    font-size: 2rem;
                    color: var(--p-text-color-secondary);
                    margin-bottom: 1rem;
                  "
                ></i>
                <p>暂无用户数据</p>
              </div>
            </template>
            <Column field="username" header="用户名" />
            <Column field="email" header="邮箱" />
            <Column field="mobile" header="手机号" />
            <Column field="department_name" header="部门" />
            <Column field="state" header="状态">
              <template #body="slotProps">
                <Tag
                  :severity="
                    userStatusSeverityMap[slotProps.data.state] || 'info'
                  "
                  :value="userStatusMap[slotProps.data.state]"
                />
              </template>
            </Column>
            <Column header="操作" :exportable="false" style="min-width: 8rem">
              <template #body="slotProps">
                <Button
                  icon="pi pi-pencil"
                  outlined
                  rounded
                  class="mr-2"
                  @click="openEdit(slotProps.data)"
                />
              </template>
            </Column>
          </DataTable>
          <div class="no-department-selected" v-else>
            <i
              class="pi pi-info-circle"
              style="
                font-size: 2rem;
                color: var(--text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>请选择左侧部门查看用户列表</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 新增用户对话框 -->
    <Drawer
      v-model:visible="userDrawer"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="false"
      :showCloseIcon="true"
      :header="editMode ? '编辑用户' : '新增用户'"
    >
      <Fluid>
        <div class="grid grid-cols-2 gap-2">
          <div class="field">
            <label for="username" class="required">用户名</label>
            <InputText
              id="username"
              v-model="userForm.username"
              required
              :class="{ 'p-invalid': submitted && !userForm.username }"
            />
            <small class="p-error" v-if="submitted && !userForm.username"
              >用户名不能为空</small
            >
          </div>
          <div class="field">
            <label for="password" class="required">密码</label>
            <Password
              id="password"
              v-model="userForm.password"
              :required="!editMode"
              fluid
              :class="{
                'p-invalid': submitted && !userForm.password && !editMode,
              }"
              toggleMask
            />
            <small
              class="p-error"
              v-if="submitted && !userForm.password && !editMode"
              >密码不能为空</small
            >
          </div>
          <div class="field">
            <label for="email" class="required">邮箱</label>
            <InputText
              id="email"
              v-model="userForm.email"
              required
              :class="{ 'p-invalid': submitted && !userForm.email }"
            />
            <small class="p-error" v-if="submitted && !userForm.email"
              >邮箱不能为空</small
            >
          </div>
          <div class="field">
            <label for="mobile" class="required">手机号</label>
            <InputText
              id="mobile"
              v-model="userForm.mobile"
              required
              :class="{ 'p-invalid': submitted && !userForm.mobile }"
            />
            <small class="p-error" v-if="submitted && !userForm.mobile"
              >手机号不能为空</small
            >
          </div>
          <div class="field">
            <label for="roles" class="required">角色</label>
            <MultiSelect
              id="roles"
              v-model="userForm.role_ids"
              filter
              fluid
              display="chip"
              :options="roles"
              optionLabel="role_name"
              optionValue="id"
              placeholder="请选择角色"
              :class="{ 'p-invalid': submitted && !userForm.role_ids.length }"
            />
            <small class="p-error" v-if="submitted && !userForm.role_ids.length"
              >请至少选择一个角色</small
            >
          </div>
          <div class="field" v-if="!selectedDepartment">
            <label for="department" class="required">部门</label>
            <TreeSelect
              id="department_id"
              :options="treeNodes"
              optionValue="key"
              :filter="true"
              fluid
              v-model="selectedValue"
              selectionMode="single"
              placeholder="请选择部门"
              :class="{ 'p-invalid': submitted && !selectedValue }"
            />
            <small class="p-error" v-if="submitted && !selectedValue"
              >请选择部门</small
            >
          </div>
        </div>
      </Fluid>
      <template #footer>
        <Button
          label="取消"
          icon="pi pi-times"
          text
          @click="hideDialog"
          class="mr-2"
        />
        <Button label="保存" icon="pi pi-check" @click="saveUser" />
      </template>
    </Drawer>
    <!-- 新增部门抽屉 -->
    <Drawer
      v-model:visible="departmentDrawerVisible"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="false"
      :showCloseIcon="true"
      :header="departmentForm.id ? '编辑部门' : '新建部门'"
    >
      <Fluid>
        <div class="grid grid-cols-1 gap-2">
          <div class="field">
            <label for="departmentName" class="required">
              部门名称
              <span v-if="fieldErrors.department_name" class="p-error ml-2">{{
                fieldErrors.department_name
              }}</span>
            </label>
            <InputText
              id="departmentName"
              v-model="departmentForm.department_name"
              placeholder="请输入部门名称"
            />
          </div>
        </div>
      </Fluid>
      <template #footer>
        <div class="flex justify-content-end">
          <Button
            label="取消"
            icon="pi pi-times"
            text
            @click="departmentDrawerVisible = false"
            class="mr-2"
          />
          <Button
            label="保存"
            icon="pi pi-check"
            class="p-button-success"
            @click="saveDepartment"
          />
        </div>
      </template>
    </Drawer>
  </div>
</template>

<style scoped>
.department-list-container {
  padding: 1rem;
  height: calc(100vh - 10rem); /* 减去上下padding的高度 */
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.department-content {
  display: flex;
  gap: 1.5rem;
  flex: 1;
  min-height: 0; /* 防止flex子元素溢出 */
}

.department-tree {
  flex: 2;
  border: 1px solid var(--surface-border);
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

.user-list {
  flex: 8;
  border: 1px solid var(--surface-border);
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
}

h4 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.tree-container {
  max-height: 70vh;
  overflow-y: auto;
  height: 90%;
  display: flex;
  flex-direction: column;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.no-department-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
  height: 300px;
}

.no-department-selected p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.user-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--surface-border);
}

.user-list-header h4 {
  margin: 0;
  padding: 0;
  border: none;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--p-primary-color);
}

.field label.required::after {
  content: " *";
  color: var(--p-red-500);
}

.p-fluid .p-button {
  width: auto;
}

.user-list-table {
  height: calc(100vh - 30rem);
  display: flex;
  flex-direction: column;
}

.p-button-sm {
  padding: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
  color: green;
}

:deep(.p-error) {
  padding: 0.5rem;
  color: red;
}
</style>
